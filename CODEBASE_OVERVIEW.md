# Codebase Overview

This document provides a more detailed look into the structure and workings of the Adam Smith Price Tracker application. It's intended for users who want to understand the internal components, perhaps to modify or extend the application.

The main `README.md` provides a general project overview, installation, and usage instructions.

## Core Components

The application is primarily built around a few key Python scripts and classes:

1.  **`smithian_price_tracker.py` (Root Level)**
    *   **Role:** This is a minimal script that serves as the primary entry point when the application is run directly from the source tree (e.g., `python smithian_price_tracker.py`).
    *   **Functionality:** Its main job is to import and execute the `main()` function from `src/smithian_price_tracker.py`.

2.  **`src/smithian_price_tracker.py` (Source Directory)**
    *   **Role:** This is the main command-line interface (CLI) handler and orchestrator of the application. It's also the target for the `smithian-tracker` command created by `setup.py`.
    *   **Functionality:**
        *   Uses `argparse` to define and parse command-line arguments (e.g., `--commodity`, `--days`, `--export-csv`).
        *   Initializes and coordinates the other main components: `CommodityData`, `SmithianAnalyzer`, and `ConsoleDisplay`.
        *   Handles the overall workflow:
            1.  Parse arguments.
            2.  If `--list-commodities` is present, display available commodities and exit.
            3.  Instantiate `CommodityData` to generate price data.
            4.  Instantiate `SmithianAnalyzer` to process this data.
            5.  Instantiate `ConsoleDisplay` to show the results.
            6.  Handle optional data export to CSV or text report.

3.  **`src/commodity_data.py`**
    *   **Role:** Responsible for generating simulated commodity price data.
    *   **Key Class:** `CommodityData`
        *   `COMMODITY_CONFIGS`: A class-level dictionary defining the available commodities, their base prices, volatility, and units. This is the primary place to add or modify commodity definitions.
        *   `__init__(self, commodities: list[str])`: Initializes with a list of commodity names to process. Validates these names against `COMMODITY_CONFIGS`.
        *   `generate_price_history(self, days: int)`: Generates a pandas DataFrame for each requested commodity. The DataFrame contains a time series of daily prices, including simulated trends, seasonality, and random noise.

4.  **`src/analyzer.py`**
    *   **Role:** Performs the core analysis of comparing market prices to a calculated "natural price."
    *   **Key Class:** `SmithianAnalyzer`
        *   `__init__(self, data_dict: Dict[str, pd.DataFrame], commodities: List[str], units_dict: Dict[str, str])`: Takes the price data (as a dictionary of DataFrames, one per commodity), a list of commodity names, and a dictionary of their units.
        *   `calculate_natural_price(self, window_days: int)`: Calculates a rolling mean (moving average) of the market price for each commodity. This rolling mean serves as the proxy for Adam Smith's "natural price." The result is stored as a new 'natural_price' column in each commodity's DataFrame.
        *   `analyze(self, window_days: int)`: For each commodity, it retrieves the latest market price and the calculated natural price. It then determines the percentage deviation and categorizes the market "condition" (e.g., `ABOVE_NATURAL`, `BELOW_NATURAL`, `NEAR_NATURAL`).
        *   `get_market_summary(self, days: int)`: Calculates recent high, low, average, and volatility for the price of each commodity over a specified number of trailing days.

5.  **`src/display.py`**
    *   **Role:** Formats and presents the analysis results to the user on the console. It also handles formatting for text file reports.
    *   **Key Class:** `ConsoleDisplay` (contains only static methods)
        *   `header()`: Prints the main application header.
        *   `analysis(analyses: List[Dict], market_summaries: Dict[str, Dict], recent_days: int)`: Takes the list of analysis dictionaries (from `SmithianAnalyzer.analyze()`) and market summaries, then prints a formatted output for each commodity, including market price, natural price, deviation, condition, and recent activity.
        *   `goodbye()`: Prints a closing message.
        *   `print_available_commodities()`: Lists all commodities defined in `CommodityData.COMMODITY_CONFIGS`.
        *   `format_analysis_for_report(...)`: Similar to `analysis()`, but formats the output as a single string suitable for writing to a text file.

## Component Interaction Flow

The general flow of information and control can be visualized as follows:

```
User Input (CLI Arguments)
      |
      v
`src/smithian_price_tracker.py` (main orchestrator)
      |
      |--(1) Parses Arguments & Validates Commodities--> `src/commodity_data.py` (CommodityData)
      |                                                  |
      |----------------(2) Generates Price Data----------|
      |
      |--(3) Sends Price Data & Analysis Parameters--> `src/analyzer.py` (SmithianAnalyzer)
      |                                                  |
      |-------------(4) Performs Analysis & Returns Results--|
      |
      |--(5) Sends Analysis Results & Summaries------> `src/display.py` (ConsoleDisplay)
      |                                                  |
      |--------------(6) Formats & Prints to Console-----|
      |
      |--(Optional 7) Exports Data/Report---------------------> File System
```

1.  The user runs `smithian-tracker` (or `python src/smithian_price_tracker.py`) with various command-line arguments.
2.  `src/smithian_price_tracker.py` parses these arguments.
3.  It instructs `CommodityData` to generate price histories for the specified commodities.
4.  The generated data (pandas DataFrames) is passed to `SmithianAnalyzer`.
5.  `SmithianAnalyzer` calculates natural prices, deviations, and market conditions.
6.  These analysis results and any market summaries are passed to `ConsoleDisplay`.
7.  `ConsoleDisplay` formats this information and prints it to the console.
8.  If requested via CLI arguments, `src/smithian_price_tracker.py` also handles exporting raw price data to CSV files or the analysis report to a text file.

This modular structure aims to separate concerns: data generation, analysis logic, and presentation are handled by different components, making the codebase easier to understand, maintain, and extend.
