import random
from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Dict

import numpy as np
import pandas as pd


class CommodityData:
    """
    Generate **simulated** daily price history for a commodity.
    The numbers are illustrative only – you can replace this
    generator with real API calls later.
    """

    COMMODITY_CONFIGS: Dict[str, Dict] = {
        "WHEAT":   {"base_price": 6.50,  "volatility": 0.03, "unit": "/bushel"},
        "COFFEE":  {"base_price": 1.80,  "volatility": 0.07, "unit": "/lb"},
        "OIL":     {"base_price": 75.00, "volatility": 0.04, "unit": "/barrel"},
        "GOLD":    {"base_price": 1950,  "volatility": 0.02, "unit": "/oz"},
        "CORN":    {"base_price": 4.25,  "volatility": 0.04, "unit": "/bushel"},
        "SILVER":  {"base_price": 24.5,  "volatility": 0.05, "unit": "/oz"},
        "COPPER":  {"base_price": 3.85,  "volatility": 0.06, "unit": "/lb"},
        "SOYBEANS": {"base_price": 12.5, "volatility": 0.04, "unit": "/bushel"},
    }

    def __init__(self, commodities: list[str]):
        self.commodities = []
        for commodity in commodities:
            name = commodity.upper()
            if name not in self.COMMODITY_CONFIGS:
                raise ValueError(
                    f"Commodity '{name}' not found in COMMODITY_CONFIGS. "
                    f"Available commodities are: {list(self.COMMODITY_CONFIGS.keys())}"
                )
            self.commodities.append(name)

    # ------------------------------------------------------------------ #
    def generate_price_history(self, days: int = 60) -> Dict[str, pd.DataFrame]:
        """
        Produce a dictionary of DataFrames, one for each commodity.
        Each DataFrame has a DateTimeIndex and 'price' column.
        Adds a mild trend, seasonality, and random noise so charts look
        realistic during demos.
        """
        price_data_dict: Dict[str, pd.DataFrame] = {}
        today = datetime.now()

        for commodity_name in self.commodities:
            config = self.COMMODITY_CONFIGS[commodity_name]
            base = config["base_price"]
            vol = config["volatility"]

            records = []
            for i in range(days):
                date = today - timedelta(days=days - i - 1)

                trend = 1 + 0.001 * (i - days / 2)
                season = 1 + 0.02 * np.sin(2 * np.pi * i / 365)

                noise = (random.random() - 0.5) * (base * vol * 2)
                if random.random() < 0.05:  # market shock
                    noise += base * random.uniform(-0.15, 0.20)

                price = max(base * trend * season + noise, base * 0.1)
                records.append({"date": date, "price": round(price, 2)})

            df = pd.DataFrame(records).set_index("date")
            price_data_dict[commodity_name] = df
        
        return price_data_dict
