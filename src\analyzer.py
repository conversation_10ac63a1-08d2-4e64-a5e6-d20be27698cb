from typing import Dict

import pandas as pd


class SmithianAnalyzer:
    """
    Compare market price series with a moving-average proxy for
    <PERSON>'s 'natural price'.
    """

    def __init__(self, data: pd.DataFrame, commodity: str, unit: str):
        self.data = data.copy()
        self.commodity = commodity.upper()
        self.unit = unit

    # ------------------------------------------------------------------ #
    def calculate_natural_price(self, window_days: int = 30) -> None:
        """Append a rolling-mean column called 'natural_price'."""
        self.data["natural_price"] = self.data["price"].rolling(
            window=window_days, min_periods=1
        ).mean()

    # ------------------------------------------------------------------ #
    def analyze(self, window_days: int = 30) -> Dict:
        """Return a dict summarising current deviation from natural price."""
        if self.data.empty:
            return {"error": "no data"}
        latest = self.data.iloc[-1]
        market = latest["price"]
        natural = latest.get("natural_price")
        result = {
            "commodity": self.commodity,
            "unit": self.unit,
            "latest_date": latest.name.strftime("%Y-%m-%d"),
            "market_price": market,
            "natural_price": natural,
            "deviation_percent": None,
        }
        if pd.notna(natural) and natural:
            dev = (market - natural) / natural * 100
            result["deviation_percent"] = dev
            if dev > 5:
                result["condition"] = "ABOVE_NATURAL"
            elif dev < -5:
                result["condition"] = "BELOW_NATURAL"
            else:
                result["condition"] = "NEAR_NATURAL"
        else:
            result["condition"] = "INSUFFICIENT_DATA"
        return result

    # ------------------------------------------------------------------ #
    def get_market_summary(self, days: int = 7) -> Dict:
        """Return summary statistics for the most recent period."""
        if self.data.empty:
            return {"error": "no data"}

        recent = self.data.tail(days)
        return {
            "period_days": days,
            "high": recent["price"].max(),
            "low": recent["price"].min(),
            "average": recent["price"].mean(),
            "volatility": recent["price"].std(),
            "start_date": recent.index[0].strftime("%Y-%m-%d") if len(recent) > 0 else None,
            "end_date": recent.index[-1].strftime("%Y-%m-%d") if len(recent) > 0 else None,
        }
