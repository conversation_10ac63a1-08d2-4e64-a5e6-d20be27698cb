from typing import Dict, List

import pandas as pd


class SmithianAnalyzer:
    """
    Compare market price series with a moving-average proxy for
    <PERSON>'s 'natural price' for multiple commodities.
    """

    def __init__(self, data_dict: Dict[str, pd.DataFrame], commodities: List[str], units_dict: Dict[str, str]):
        self.data_dict = {name.upper(): df.copy() for name, df in data_dict.items()}
        self.commodities_list = [name.upper() for name in commodities]
        self.units_dict = {name.upper(): unit for name, unit in units_dict.items()}

    # ------------------------------------------------------------------ #
    def calculate_natural_price(self, window_days: int = 30) -> None:
        """Calculate and append a 'natural_price' column for each commodity's DataFrame."""
        for commodity_name in self.commodities_list:
            if commodity_name in self.data_dict:
                df = self.data_dict[commodity_name]
                df["natural_price"] = df["price"].rolling(
                    window=window_days, min_periods=1
                ).mean()
            else:
                # Potentially handle or log missing data for a commodity
                print(f"Warning: Data for commodity '{commodity_name}' not found in data_dict.")


    # ------------------------------------------------------------------ #
    def analyze(self, window_days: int = 30) -> List[Dict]:
        """Return a list of dicts, each summarising current deviation from natural price for a commodity."""
        self.calculate_natural_price(window_days)  # Ensure natural prices are calculated

        analysis_results = []
        for commodity_name in self.commodities_list:
            if commodity_name not in self.data_dict or self.data_dict[commodity_name].empty:
                analysis_results.append({"commodity": commodity_name, "error": "no data"})
                continue

            commodity_df = self.data_dict[commodity_name]
            unit = self.units_dict.get(commodity_name, "N/A") # Default unit if not found
            
            latest = commodity_df.iloc[-1]
            market = latest["price"]
            natural = latest.get("natural_price")
            
            result = {
                "commodity": commodity_name,
                "unit": unit,
                "latest_date": latest.name.strftime("%Y-%m-%d"),
                "market_price": market,
                "natural_price": natural,
                "deviation_percent": None,
                "condition": "INSUFFICIENT_DATA", # Default condition
            }

            if pd.notna(natural) and natural != 0: # Avoid division by zero
                dev = (market - natural) / natural * 100
                result["deviation_percent"] = dev
                if dev > 5:
                    result["condition"] = "ABOVE_NATURAL"
                elif dev < -5:
                    result["condition"] = "BELOW_NATURAL"
                else:
                    result["condition"] = "NEAR_NATURAL"
            
            analysis_results.append(result)
        return analysis_results

    # ------------------------------------------------------------------ #
    def get_market_summary(self, days: int = 7) -> Dict[str, Dict]:
        """Return a dict of market summaries, keyed by commodity name."""
        market_summaries: Dict[str, Dict] = {}
        for commodity_name in self.commodities_list:
            if commodity_name not in self.data_dict or self.data_dict[commodity_name].empty:
                market_summaries[commodity_name] = {"error": "no data"}
                continue

            commodity_df = self.data_dict[commodity_name]
            recent = commodity_df.tail(days)
            
            if recent.empty:
                 market_summaries[commodity_name] = {
                    "error": "no data for the requested period",
                    "period_days": days,
                 }
                 continue

            market_summaries[commodity_name] = {
                "period_days": days,
                "high": recent["price"].max(),
                "low": recent["price"].min(),
                "average": round(recent["price"].mean(), 2),
                "volatility": round(recent["price"].std(), 2),
                "start_date": recent.index[0].strftime("%Y-%m-%d") if not recent.empty else None,
                "end_date": recent.index[-1].strftime("%Y-%m-%d") if not recent.empty else None,
            }
        return market_summaries
