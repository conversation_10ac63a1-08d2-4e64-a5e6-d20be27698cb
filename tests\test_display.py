import unittest
from typing import Dict, List
from src.display import ConsoleDisplay # Assuming ConsoleDisplay is in src.display

class TestConsoleDisplay(unittest.TestCase):

    def test_format_analysis_for_report(self):
        sample_analyses: List[Dict] = [
            {
                "commodity": "GOLD",
                "unit": "/oz",
                "latest_date": "2024-01-10",
                "market_price": 1850.50,
                "natural_price": 1800.00,
                "deviation_percent": 2.8055, # (1850.50 - 1800) / 1800 * 100
                "condition": "ABOVE_NATURAL"
            },
            {
                "commodity": "SILVER",
                "unit": "/oz",
                "latest_date": "2024-01-10",
                "market_price": 22.50,
                "natural_price": None, # Or some indicator it's not calculated
                "deviation_percent": None,
                "condition": "INSUFFICIENT_DATA"
            }
        ]

        sample_market_summaries: Dict[str, Dict] = {
            "GOLD": {
                "high": 1860.00,
                "low": 1840.00,
                "average": 1850.25
            },
            "SILVER": { # Summary might still exist even if natural price doesn't
                "high": 23.00,
                "low": 22.00,
                "average": 22.60
            }
        }
        
        recent_days = 7

        report_string = ConsoleDisplay.format_analysis_for_report(
            sample_analyses, sample_market_summaries, recent_days
        )

        # Main header
        self.assertIn("SMITHIAN PRICE TRACKER", report_string)
        self.assertIn("=" * 70, report_string) # Check for header/footer lines

        # Commodity 1: GOLD
        self.assertIn("Commodity: GOLD", report_string)
        self.assertIn(ConsoleDisplay.LINE, report_string) # Check for section separator
        self.assertIn("Date ........... 2024-01-10", report_string)
        self.assertIn("Market Price ... $1850.50 /oz", report_string)
        self.assertIn("Natural Price .. $1800.00 /oz", report_string)
        self.assertIn("Deviation ...... +2.81% (ABOVE_NATURAL)", report_string) # Note: + sign and rounded
        
        self.assertIn(f"Recent activity (last {recent_days} days)", report_string)
        self.assertIn("High: $1860.00   Low: $1840.00   Avg: $1850.25", report_string)

        # Commodity 2: SILVER
        self.assertIn("Commodity: SILVER", report_string)
        self.assertIn("Market Price ... $22.50 /oz", report_string)
        self.assertIn("Natural Price .. not enough history or data.", report_string)
        
        self.assertIn(f"Recent activity (last {recent_days} days)", report_string)
        self.assertIn("High: $23.00   Low: $22.00   Avg: $22.60", report_string)
        
        # Check for spacing between commodities
        # This is a bit fragile, depends on exact number of newlines.
        # A more robust check might be to split lines and look for empty lines.
        self.assertTrue(report_string.count("\n\nCommodity:") >= 1 or len(sample_analyses) <=1)


if __name__ == '__main__':
    unittest.main()
