# Modifying and Extending the Application

The Adam Smith Price Tracker is designed to be extensible. This guide provides details on how you can modify various aspects of the application, from adding new commodities to changing the analysis logic or even integrating real-world data.

Refer to `CODEBASE_OVERVIEW.md` for a detailed understanding of the different components mentioned here.

## 1. Adding New Commodities

Adding a new commodity to the simulation is straightforward:

*   **Locate `src/commodity_data.py`:** This file contains the `CommodityData` class.
*   **Edit `COMMODITY_CONFIGS`:** Inside the `CommodityData` class, you'll find a dictionary named `COMMODITY_CONFIGS`.
    ```python
    class CommodityData:
        COMMODITY_CONFIGS: Dict[str, Dict] = {
            "WHEAT":   {"base_price": 6.50,  "volatility": 0.03, "unit": "/bushel"},
            "COFFEE":  {"base_price": 1.80,  "volatility": 0.07, "unit": "/lb"},
            # ... other commodities
        }
        # ...
    ```
*   **Add a new entry:** To add a new commodity, simply add a new key-value pair to this dictionary. The key is the commodity's name (conventionally in uppercase). The value is another dictionary with the following keys:
    *   `base_price` (float): The average price around which the simulation will generate data.
    *   `volatility` (float): A factor influencing the randomness and range of price fluctuations. Higher values mean more price swings.
    *   `unit` (str): The unit of measurement for the commodity (e.g., "/bushel", "/lb", "/barrel"). This is used in the display.

*   **Example:** To add "SUGAR" with a base price of $0.20/lb and volatility of 0.05:
    ```python
    "SUGAR":   {"base_price": 0.20,  "volatility": 0.05, "unit": "/lb"},
    ```
*   **Save the file.** The new commodity will now be available for selection via the `--commodity` CLI option and will appear in the `--list-commodities` output.

## 2. Integrating Real Price Data

Currently, `CommodityData.generate_price_history()` simulates price data. To use real-world data, you would need to modify this method.

*   **Conceptual Changes:**
    1.  **Choose a Data Source:** Identify an API or data source that provides historical commodity prices (e.g., Alpha Vantage, Quandl, or other financial data providers). Many require API keys and have usage limits.
    2.  **Modify `generate_price_history()`:**
        *   Instead of generating data randomly, this method would make API calls to fetch data for the requested commodities and date range.
        *   You'll likely need to add new libraries for making HTTP requests (e.g., `requests`) and potentially for handling API-specific authentication. These would also need to be added to `requirements.txt`.
    3.  **Data Formatting:** The fetched data must be transformed into a pandas DataFrame with a `DateTimeIndex` (named 'date') and a 'price' column, consistent with what the `SmithianAnalyzer` expects.
    4.  **Error Handling:** Implement robust error handling for API request failures, rate limits, missing data for certain dates/commodities, etc.
    5.  **Parameter Adjustments:** The `--days` parameter might now map to the length of historical data to fetch.

*   **Note:** This is a significant modification and would require careful planning regarding the chosen data source's terms of service and data format.

## 3. Modifying Analysis Logic

The core analysis logic resides in `src/analyzer.py` within the `SmithianAnalyzer` class.

*   **Changing Natural Price Calculation:**
    *   The "natural price" is currently a simple moving average, calculated in `calculate_natural_price()`.
        ```python
        df["natural_price"] = df["price"].rolling(
            window=window_days, min_periods=1
        ).mean()
        ```
    *   You could change `window_days` (though it's also a CLI parameter) or replace `.mean()` with other pandas rolling functions like `.median()`, `.ewm().mean()` (exponential moving average), or implement a custom calculation.

*   **Adjusting Deviation Thresholds:**
    *   In the `analyze()` method, the market "condition" (`ABOVE_NATURAL`, `BELOW_NATURAL`, `NEAR_NATURAL`) is determined by fixed percentage deviations (+/- 5%).
        ```python
        if dev > 5:
            result["condition"] = "ABOVE_NATURAL"
        elif dev < -5:
            result["condition"] = "BELOW_NATURAL"
        else:
            result["condition"] = "NEAR_NATURAL"
        ```
    *   You can change these thresholds (e.g., to 7% or 3%) or add more nuanced conditions.

*   **Adding New Metrics:**
    *   You could extend `analyze()` or `get_market_summary()` to calculate and return additional statistics (e.g., price momentum, number of times price crosses the natural price). These would then also need to be handled by `src/display.py` if you want them shown.

## 4. Customizing Console Output

The console output is managed by `src/display.py` and its `ConsoleDisplay` class.

*   **Modifying Text and Formatting:**
    *   The static methods in `ConsoleDisplay` (e.g., `analysis()`, `header()`, `print_available_commodities()`) use simple `print()` statements and string formatting.
    *   You can change the text, layout, or use of lines (`ConsoleDisplay.LINE`) directly in these methods.
    *   For example, to change how prices are displayed, you would modify the f-string formatting within the `analysis()` method.

*   **Adding More Information:**
    *   If you've added new metrics in `SmithianAnalyzer`, you'll need to update `ConsoleDisplay.analysis()` (and potentially `ConsoleDisplay.format_analysis_for_report()`) to include these new data points in the output. This involves:
        1.  Ensuring the new data is passed to these methods.
        2.  Adding `print()` statements or modifying existing ones to display the new information.

## General Advice for Modifications

*   **Understand the Flow:** Before making changes, ensure you understand how data flows between `commodity_data.py`, `analyzer.py`, and `display.py` (see `CODEBASE_OVERVIEW.md`).
*   **Testing:** If you make significant changes, especially to the analysis logic, consider adding or modifying tests in the `tests/` directory to verify your changes behave as expected. Run tests using `python -m unittest discover tests`.
*   **Dependencies:** If you add new external libraries, remember to add them to `requirements.txt` and consider updating `setup.py` if necessary.
*   **Virtual Environments:** Always work within an activated virtual environment to manage dependencies cleanly.

By understanding these key areas, you should be able to tailor the Adam Smith Price Tracker to better suit your specific interests or educational goals.
