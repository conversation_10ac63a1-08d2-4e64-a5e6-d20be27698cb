from typing import Dict

import pandas as pd


class ConsoleDisplay:
    """Utility functions for pretty console output."""

    LINE = "-" * 50

    @staticmethod
    def header() -> None:
        print("=" * 70)
        print("SMITHIAN PRICE TRACKER".center(70))
        print("=" * 70)

    # ------------------------------------------------------------------ #
    @staticmethod
    def analysis(analysis: Dict, df: pd.DataFrame, recent_days: int = 7) -> None:
        """Print analysis result plus recent stats."""
        ConsoleDisplay.header()

        print(f"\nCommodity: {analysis['commodity']}")
        print(ConsoleDisplay.LINE)
        print(f"Date ........... {analysis['latest_date']}")
        print(
            f"Market Price ... ${analysis['market_price']:.2f} {analysis['unit']}"
        )

        if analysis["condition"] != "INSUFFICIENT_DATA":
            print(
                f"Natural Price .. ${analysis['natural_price']:.2f} "
                f"{analysis['unit']}"
            )
            if analysis["deviation_percent"] is not None:
                print(
                    f"Deviation ...... {analysis['deviation_percent']:+.2f}% "
                    f"({analysis['condition']})"
                )
        else:
            print("Natural Price .. not enough history.")

        # recent stats
        recent = df.tail(recent_days)
        print("\nRecent activity (last", recent_days, "days)")
        print(ConsoleDisplay.LINE)
        print(
            f"High: ${recent['price'].max():.2f}   "
            f"Low: ${recent['price'].min():.2f}   "
            f"Avg: ${recent['price'].mean():.2f}"
        )

    # ------------------------------------------------------------------ #
    @staticmethod
    def goodbye() -> None:
        print("\n" + "=" * 70)
        print("Thank you for using Smithian Price Tracker!".center(70))
        print("=" * 70 + "\n")
