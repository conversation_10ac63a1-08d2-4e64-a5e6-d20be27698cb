# Contributing Guidelines

Thank you for considering contributing to the Adam Smith Price Tracker! We welcome contributions that improve the application, fix bugs, or enhance its educational value.

## Getting Started

1.  **Fork the Repository:** Start by forking the [main repository](https://github.com/nicholas<PERSON>lson/adam-smith-price-tracker) to your GitHub account.
2.  **Clone Your Fork:** Clone your forked repository to your local machine:
    ```bash
    git clone https://github.com/YOUR_USERNAME/adam-smith-price-tracker.git
    cd adam-smith-price-tracker
    ```
3.  **Set Up Development Environment:**
    *   Follow the installation instructions in the main `README.md` to create a virtual environment and install dependencies from `requirements.txt`.
    *   It's also recommended to install the package in editable mode if you're making changes to the code:
        ```bash
        pip install -e .
        ```
        This allows your changes in the `src` directory to be immediately reflected when you run `smithian-tracker`.

## Making Changes

1.  **Create a New Branch:** Before making any changes, create a new branch from the `main` branch (or the most up-to-date development branch):
    ```bash
    git checkout main
    git pull origin main  # Ensure your main branch is up-to-date
    git checkout -b your-feature-branch-name
    ```
    Choose a descriptive branch name (e.g., `add-sugar-commodity`, `fix-csv-export-bug`).

2.  **Write Code:** Make your changes to the codebase.
    *   Refer to `CODEBASE_OVERVIEW.md` to understand the structure and components.
    *   Refer to `MODIFYING_THE_APP.md` for guidance on common modifications.

3.  **Coding Style:**
    *   Try to follow PEP 8 guidelines for Python code. Most modern editors can be configured to help with this.
    *   Keep lines to a reasonable length (e.g., under 100 characters).
    *   Add comments to explain complex logic.
    *   Ensure your code is clear and readable.

4.  **Testing:**
    *   Run existing tests to ensure your changes haven't broken existing functionality. Navigate to the project's root directory and execute:
        ```bash
        python -m unittest discover tests
        ```
    *   If you add new features or fix a bug, please consider adding new unit tests for your changes in the `tests/` directory. This helps ensure the reliability of the codebase.

## Submitting Your Contributions

1.  **Commit Your Changes:** Make clear, concise commit messages.
    ```bash
    git add .
    git commit -m "feat: Add SUGAR as a new commodity"
    # or
    git commit -m "fix: Corrected calculation for natural price under X condition"
    ```
    Consider using [Conventional Commits](https://www.conventionalcommits.org/) style for commit messages if you are familiar with it (e.g., `feat:`, `fix:`, `docs:`, `refactor:`).

2.  **Push to Your Fork:** Push your changes to your forked repository on GitHub:
    ```bash
    git push origin your-feature-branch-name
    ```

3.  **Open a Pull Request (PR):**
    *   Go to your fork on GitHub.
    *   Click the "Compare & pull request" button for your new branch.
    *   Ensure the base repository and branch are set to the original repository's `main` branch.
    *   Provide a clear title and description for your PR. Explain what changes you've made and why. If your PR addresses an open issue, link to it (e.g., "Closes #123").
    *   Submit the PR.

## Code Review

*   Once your PR is submitted, project maintainers will review your changes.
*   Be prepared to discuss your changes and make adjustments based on feedback.
*   Once the review is complete and any CI checks pass, your contribution may be merged!

## Reporting Bugs or Suggesting Features

*   If you find a bug or have an idea for a new feature, please check the [Issue Tracker](https://github.com/nicholaskarlson/adam-smith-price-tracker/issues) to see if it has already been reported or suggested.
*   If not, feel free to open a new issue. Provide as much detail as possible:
    *   For bugs: Steps to reproduce, expected behavior, actual behavior, error messages, your operating system/Python version.
    *   For features: A clear description of the proposed feature and its benefits.

Thank you for helping to improve the Adam Smith Price Tracker!
```
