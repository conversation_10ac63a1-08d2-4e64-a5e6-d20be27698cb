Metadata-Version: 2.1
Name: adam-smith-price-tracker
Version: 1.0.0
Summary: Console application demonstrating <PERSON>'s market-versus-natural price concept with simulated commodity data.
Home-page: https://github.com/nicholaskarlson/adam-smith-price-tracker
Author: <PERSON>
Author-email: <PERSON><PERSON><PERSON><PERSON>@gmail.com
License: MIT
Project-URL: Bug Tracker, https://github.com/nicholaskarlson/adam-smith-price-tracker/issues
Project-URL: Source, https://github.com/nicholaskarlson/adam-smith-price-tracker
Keywords: economics,adam-smith,commodity-prices,market-analysis,finance,education
Platform: UNKNOWN
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Financial and Insurance Industry
Classifier: Topic :: Office/Business :: Financial
Classifier: Topic :: Education
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Operating System :: OS Independent
Requires-Python: >=3.7
Description-Content-Type: text/markdown

# Adam <PERSON> Price Tracker

A console application that illustrates <PERSON>’s “market-versus-natural” price
concept using simulated commodity data.  
Run `smithian-tracker --help` to see available options.

*Quick start*

```bash
python -m venv venv
source venv/bin/activate   # Windows: venv\Scripts\activate
pip install -r requirements.txt
python smithian_price_tracker.py --commodity WHEAT


