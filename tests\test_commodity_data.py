import unittest
import pandas as pd
from datetime import datetime, timedelta

# Adjust the import path based on your project structure
# If tests are directly under the project root, and src is a sibling:
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
from commodity_data import CommodityData


class TestCommodityData(unittest.TestCase):

    def test_init_successful_multiple_commodities(self):
        """Test initialization with a list of valid commodity names."""
        commodities = ["WHEAT", "GOLD"]
        cd = CommodityData(commodities)
        self.assertEqual(cd.commodities, ["WHEAT", "GOLD"])

    def test_init_successful_single_commodity_in_list(self):
        """Test initialization with a single valid commodity in a list."""
        commodities = ["COFFEE"]
        cd = CommodityData(commodities)
        self.assertEqual(cd.commodities, ["COFFEE"])
        # Check if the config for COFFEE is accessible indirectly
        # This part is more about ensuring the internal state allows generating data
        try:
            cd.generate_price_history(days=1) # Should not raise error if config is found
        except Exception as e:
            self.fail(f"generate_price_history failed for single commodity init: {e}")


    def test_init_invalid_commodity(self):
        """Test that ValueError is raised for invalid commodity names."""
        with self.assertRaises(ValueError) as context:
            CommodityData(["WHEAT", "NONEXISTENT"])
        self.assertIn("NONEXISTENT", str(context.exception))
        self.assertIn("not found in COMMODITY_CONFIGS", str(context.exception))

    def test_init_invalid_commodity_among_valid(self):
        """Test ValueError with a mix of valid and invalid commodities."""
        with self.assertRaises(ValueError) as context:
            CommodityData(["GOLD", "INVALID_COFFEE", "WHEAT"])
        self.assertIn("INVALID_COFFEE", str(context.exception))

    def test_init_empty_list(self):
        """Test initialization with an empty list of commodities."""
        # The current implementation allows an empty list.
        # generate_price_history should then return an empty dict.
        commodities = []
        cd = CommodityData(commodities)
        self.assertEqual(cd.commodities, [])
        history = cd.generate_price_history(days=10)
        self.assertEqual(history, {})

    def test_generate_price_history_multi_commodity(self):
        """Test generating price history for multiple commodities."""
        commodities_to_test = ["WHEAT", "COFFEE"]
        cd = CommodityData(commodities_to_test)
        days_to_generate = 10
        history = cd.generate_price_history(days=days_to_generate)

        self.assertIsInstance(history, dict, "Should return a dictionary.")
        self.assertEqual(set(history.keys()), set(commodities_to_test),
                         "Dictionary keys should match commodity names.")

        for commodity_name in commodities_to_test:
            self.assertIn(commodity_name, history)
            df = history[commodity_name]
            self.assertIsInstance(df, pd.DataFrame, f"Value for {commodity_name} should be a DataFrame.")
            self.assertIsInstance(df.index, pd.DatetimeIndex, f"Index for {commodity_name} should be DatetimeIndex.")
            self.assertIn("price", df.columns, f"'price' column missing for {commodity_name}.")
            self.assertEqual(len(df), days_to_generate, f"DataFrame for {commodity_name} should have {days_to_generate} rows.")
            self.assertTrue(all(df["price"] > 0), f"Prices for {commodity_name} should be positive.")

        # Test if data for different commodities is independent (prices are different)
        # This is a probabilistic check, but with different base prices, means should differ.
        if len(commodities_to_test) > 1:
            df1_prices = history[commodities_to_test[0]]["price"]
            df2_prices = history[commodities_to_test[1]]["price"]
            # A simple check: the dataframes themselves should not be identical if commodities are different
            self.assertFalse(history[commodities_to_test[0]].equals(history[commodities_to_test[1]]),
                             "DataFrames for different commodities should not be identical.")
            # A more robust check might involve comparing mean prices if base prices are significantly different
            mean_price_differs = (df1_prices.mean() != df2_prices.mean())
            # If they happen to be the same due to randomness, check if configs are different
            if not mean_price_differs:
                 config1_base = CommodityData.COMMODITY_CONFIGS[commodities_to_test[0]]['base_price']
                 config2_base = CommodityData.COMMODITY_CONFIGS[commodities_to_test[1]]['base_price']
                 self.assertNotEqual(config1_base, config2_base, 
                                     f"Mean prices were the same for {commodities_to_test[0]} and {commodities_to_test[1]}, "
                                     "and their base prices are also the same, making the independence check unreliable here.")


if __name__ == '__main__':
    unittest.main()
