#!/usr/bin/env python3
"""
Smithian Price Tracker – console application
Illustrates <PERSON>'s “market‐vs‐natural” price concept
using simulated (or future real-world) commodity data.

After installing the package you can run:
    smithian-tracker --commodity WHEAT

`smithian-tracker` is provided by the console-script entry-point
configured in setup.py.  During development you may also execute:

    python smithian_price_tracker.py --help
"""

import argparse
import sys

from commodity_data import CommodityData
from analyzer import Smithian<PERSON>nal<PERSON><PERSON>
from display import ConsoleDisplay


# --------------------------------------------------------------------------- #
def build_cli() -> argparse.ArgumentParser:
    """Return a configured ArgParser."""
    parser = argparse.ArgumentParser(
        prog="smithian-tracker",
        description=(
            "Analyze simulated commodity prices against a moving-average "
            "proxy for <PERSON>’s ‘natural price’."
        ),
        formatter_class=argparse.RawTextHelpFormatter,
        epilog="""
Examples
--------
smithian-tracker --commodity WHEAT
smithian-tracker --commodity GOLD --window 45 --days 90
smithian-tracker --list-commodities
""",
    )

    parser.add_argument("-c", "--commodity", default="WHEAT",
                        help="Commodity symbol to analyze (default: WHEAT)")
    parser.add_argument("-w", "--window", type=int, default=30,
                        help="Rolling window (days) for natural-price proxy")
    parser.add_argument("-d", "--days", type=int, default=60,
                        help="Length of price history to simulate (days)")
    parser.add_argument("-s", "--summary-days", type=int, default=7,
                        help="Recent period for summary stats (days)")
    parser.add_argument("-l", "--list-commodities", action="store_true",
                        help="List available commodities and exit")
    parser.add_argument("--export-csv", metavar="PATH",
                        help="Export generated price data to CSV")
    return parser


# --------------------------------------------------------------------------- #
def run_tracker(args: argparse.Namespace) -> None:
    """Core workflow: generate data, analyze, display, (optional) export."""
    ConsoleDisplay.header()

    if args.list_commodities:
        ConsoleDisplay.print_available_commodities()
        return

    commodity = args.commodity.upper()
    if commodity not in CommodityData.COMMODITY_CONFIGS:
        print(f"\n❌  '{commodity}' is not a supported commodity.")
        ConsoleDisplay.print_available_commodities()
        sys.exit(1)

    print(f"\n🔄  Generating {args.days} days of {commodity} prices ...")
    generator = CommodityData(commodity)
    df = generator.generate_price_history(args.days)

    analyzer = SmithianAnalyzer(df, commodity, generator.config["unit"])
    analyzer.calculate_natural_price(window_days=args.window)

    analysis_result = analyzer.analyze(window_days=args.window)
    summary_stats = analyzer.get_market_summary(days=args.summary_days)

    ConsoleDisplay.analysis(analysis_result, analyzer.data,
                            recent_days=args.summary_days)
    ConsoleDisplay.goodbye()

    if args.export_csv:
        analyzer.data.to_csv(args.export_csv, index=True)
        print(f"💾  Data exported to {args.export_csv}")


# --------------------------------------------------------------------------- #
def main() -> None:
    run_tracker(build_cli().parse_args())


if __name__ == "__main__":
    main()
