import unittest
import pandas as pd
from datetime import datetime

# Adjust the import path based on your project structure
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
from analyzer import SmithianAnalyzer
# CommodityData might be needed if generating test data within tests, but not for direct testing of Analyzer
# from commodity_data import CommodityData 

class TestSmithianAnalyzerMultiCommodity(unittest.TestCase):

    def setUp(self):
        """Set up multi-commodity test data before each test."""
        self.dates = pd.to_datetime([datetime(2023, 1, i) for i in range(1, 11)]) # 10 dates
        
        self.wheat_prices = [10.0, 11.0, 12.0, 11.0, 10.0, 10.5, 11.5, 12.5, 11.5, 10.5]
        self.gold_prices = [100.0, 101.0, 102.0, 101.0, 100.0, 100.5, 101.5, 102.5, 101.5, 100.5]

        self.wheat_df = pd.DataFrame({"price": self.wheat_prices}, index=self.dates)
        self.gold_df = pd.DataFrame({"price": self.gold_prices}, index=self.dates)

        self.data_dict = {
            "WHEAT": self.wheat_df.copy(),
            "GOLD": self.gold_df.copy()
        }
        self.commodities_list = ["WHEAT", "GOLD"]
        self.units_dict = {"WHEAT": "/bushel", "GOLD": "/oz"}

        # Default analyzer instance for common tests
        self.analyzer = SmithianAnalyzer(self.data_dict, self.commodities_list, self.units_dict)

    def test_init_multi_commodity(self):
        """Test correct initialization of SmithianAnalyzer with multiple commodities."""
        self.assertTrue(self.analyzer.data_dict["WHEAT"].equals(self.wheat_df))
        self.assertTrue(self.analyzer.data_dict["GOLD"].equals(self.gold_df))
        self.assertEqual(self.analyzer.commodities_list, self.commodities_list)
        self.assertEqual(self.analyzer.units_dict, self.units_dict)

    def test_calculate_natural_price_multi(self):
        """Test calculation of 'natural_price' for multiple commodities."""
        window = 3
        self.analyzer.calculate_natural_price(window_days=window)

        for commodity_name in self.commodities_list:
            self.assertIn("natural_price", self.analyzer.data_dict[commodity_name].columns)
            # Check a specific calculation for the last point
            # For WHEAT: prices [12.5, 11.5, 10.5], natural = (12.5+11.5+10.5)/3 = 11.5
            # For GOLD: prices [102.5, 101.5, 100.5], natural = (102.5+101.5+100.5)/3 = 101.5
            expected_last_natural_price = pd.Series(self.data_dict[commodity_name]["price"][-window:]).mean()
            actual_last_natural_price = self.analyzer.data_dict[commodity_name]["natural_price"].iloc[-1]
            self.assertAlmostEqual(actual_last_natural_price, expected_last_natural_price, places=2)

            # Check first point (min_periods=1)
            expected_first_natural_price = self.data_dict[commodity_name]["price"].iloc[0]
            actual_first_natural_price = self.analyzer.data_dict[commodity_name]["natural_price"].iloc[0]
            self.assertAlmostEqual(actual_first_natural_price, expected_first_natural_price, places=2)


    def test_analyze_multi(self):
        """Test the main analysis method for multiple commodities."""
        window = 3
        results = self.analyzer.analyze(window_days=window)

        self.assertIsInstance(results, list)
        self.assertEqual(len(results), len(self.commodities_list))

        for commodity_name in self.commodities_list:
            result = next((r for r in results if r["commodity"] == commodity_name), None)
            self.assertIsNotNone(result)
            self.assertEqual(result["unit"], self.units_dict[commodity_name])
            
            latest_market_price = self.data_dict[commodity_name]["price"].iloc[-1]
            self.assertEqual(result["market_price"], latest_market_price)

            # Expected natural price for the last day
            expected_natural = pd.Series(self.data_dict[commodity_name]["price"][-window:]).mean()
            self.assertAlmostEqual(result["natural_price"], expected_natural, places=2)

            expected_dev = (latest_market_price - expected_natural) / expected_natural * 100
            self.assertAlmostEqual(result["deviation_percent"], expected_dev, places=2)
            
            if expected_dev > 5:
                self.assertEqual(result["condition"], "ABOVE_NATURAL")
            elif expected_dev < -5:
                self.assertEqual(result["condition"], "BELOW_NATURAL")
            else:
                self.assertEqual(result["condition"], "NEAR_NATURAL")

    def test_analyze_insufficient_data_one_commodity(self):
        """Test analysis when one commodity has too few data points for the window."""
        short_oil_df = pd.DataFrame({"price": [70.0, 71.0]}, index=self.dates[:2]) # Only 2 days
        
        custom_data_dict = {"WHEAT": self.wheat_df.copy(), "OIL": short_oil_df}
        custom_commodities = ["WHEAT", "OIL"]
        custom_units = {"WHEAT": "/bushel", "OIL": "/barrel"}
        
        analyzer = SmithianAnalyzer(custom_data_dict, custom_commodities, custom_units)
        results = analyzer.analyze(window_days=3) # Window is 3, OIL has 2 days

        oil_result = next(r for r in results if r["commodity"] == "OIL")
        wheat_result = next(r for r in results if r["commodity"] == "WHEAT")

        self.assertEqual(oil_result["condition"], "INSUFFICIENT_DATA")
        self.assertTrue(pd.isna(oil_result["natural_price"]) or oil_result["natural_price"] is None)
        self.assertTrue(pd.isna(oil_result["deviation_percent"]) or oil_result["deviation_percent"] is None)
        
        # Wheat should still be analyzed correctly
        self.assertNotEqual(wheat_result["condition"], "INSUFFICIENT_DATA")
        self.assertIsNotNone(wheat_result["natural_price"])


    def test_analyze_empty_data_one_commodity(self):
        """Test analysis when one commodity has an empty DataFrame."""
        empty_silver_df = pd.DataFrame(columns=["price"], index=pd.to_datetime([]))
        
        custom_data_dict = {"WHEAT": self.wheat_df.copy(), "SILVER": empty_silver_df}
        custom_commodities = ["WHEAT", "SILVER"]
        custom_units = {"WHEAT": "/bushel", "SILVER": "/oz"}

        analyzer = SmithianAnalyzer(custom_data_dict, custom_commodities, custom_units)
        results = analyzer.analyze(window_days=3)

        silver_result = next(r for r in results if r["commodity"] == "SILVER")
        self.assertEqual(silver_result.get("error"), "no data")
        self.assertNotIn("market_price", silver_result) # Or check if it's None

    def test_get_market_summary_multi(self):
        """Test market summary generation for multiple commodities."""
        summary_days = 5
        summaries = self.analyzer.get_market_summary(days=summary_days)

        self.assertIsInstance(summaries, dict)
        self.assertEqual(set(summaries.keys()), set(self.commodities_list))

        for commodity_name in self.commodities_list:
            summary = summaries[commodity_name]
            recent_prices = self.data_dict[commodity_name]["price"].tail(summary_days)
            
            self.assertEqual(summary["period_days"], summary_days)
            self.assertAlmostEqual(summary["high"], recent_prices.max(), places=2)
            self.assertAlmostEqual(summary["low"], recent_prices.min(), places=2)
            self.assertAlmostEqual(summary["average"], round(recent_prices.mean(), 2), places=2)
            self.assertAlmostEqual(summary["volatility"], round(recent_prices.std(), 2), places=2)
            self.assertEqual(summary["start_date"], recent_prices.index[0].strftime("%Y-%m-%d"))
            self.assertEqual(summary["end_date"], recent_prices.index[-1].strftime("%Y-%m-%d"))

    def test_get_market_summary_empty_data_one_commodity(self):
        """Test market summary when one commodity has an empty DataFrame."""
        empty_silver_df = pd.DataFrame(columns=["price"], index=pd.to_datetime([]))
        custom_data_dict = {"WHEAT": self.wheat_df.copy(), "SILVER": empty_silver_df}
        custom_commodities = ["WHEAT", "SILVER"]
        custom_units = {"WHEAT": "/bushel", "SILVER": "/oz"}
        
        analyzer = SmithianAnalyzer(custom_data_dict, custom_commodities, custom_units)
        summaries = analyzer.get_market_summary(days=5)

        self.assertIn("SILVER", summaries)
        self.assertEqual(summaries["SILVER"].get("error"), "no data")
        
        # WHEAT should still have a valid summary
        self.assertNotIn("error", summaries["WHEAT"])
        self.assertEqual(summaries["WHEAT"]["period_days"], 5)

    def test_get_market_summary_not_enough_data_for_full_period(self):
        """Test market summary when data is less than summary_days but not empty."""
        # OIL has 2 days of data
        short_oil_df = pd.DataFrame({"price": [70.0, 71.0]}, index=self.dates[:2])
        custom_data_dict = {"OIL": short_oil_df}
        custom_commodities = ["OIL"]
        custom_units = {"OIL": "/barrel"}
        
        analyzer = SmithianAnalyzer(custom_data_dict, custom_commodities, custom_units)
        summary_days_requested = 5
        summaries = analyzer.get_market_summary(days=summary_days_requested)
        
        self.assertIn("OIL", summaries)
        oil_summary = summaries["OIL"]
        
        self.assertNotIn("error", oil_summary) # Should still provide summary for available data
        self.assertEqual(oil_summary["period_days"], summary_days_requested) # reports requested days
        self.assertAlmostEqual(oil_summary["high"], 71.0, places=2)
        self.assertAlmostEqual(oil_summary["low"], 70.0, places=2)
        self.assertAlmostEqual(oil_summary["average"], 70.5, places=2)
        # Volatility of 2 points: std([70,71]) = sqrt(((70-70.5)^2 + (71-70.5)^2)/(2-1)) = sqrt(0.25+0.25) = sqrt(0.5) approx 0.707
        self.assertAlmostEqual(oil_summary["volatility"], round(pd.Series([70.0, 71.0]).std(),2), places=2)
        self.assertEqual(oil_summary["start_date"], self.dates[0].strftime("%Y-%m-%d"))
        self.assertEqual(oil_summary["end_date"], self.dates[1].strftime("%Y-%m-%d"))

if __name__ == '__main__':
    unittest.main()
