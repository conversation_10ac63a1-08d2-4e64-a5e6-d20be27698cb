import pandas as pd
from src.analyzer import SmithianAnalyzer


def test_deviation():
    df = pd.DataFrame({"price": [10, 11, 12]})
    df.index = pd.date_range("2025-01-01", periods=3)
    sa = SmithianAnalyzer(df, "TEST", "/unit")
    sa.calculate_natural_price(window_days=2)
    result = sa.analyze(window_days=2)
    assert result["condition"] == "ABOVE_NATURAL"
    assert result["deviation_percent"] > 0
