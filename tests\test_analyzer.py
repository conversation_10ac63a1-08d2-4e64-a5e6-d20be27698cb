import pandas as pd
from src.analyzer import SmithianAnalyzer


def test_deviation():
    df = pd.DataFrame({"price": [10, 11, 12]})
    df.index = pd.date_range("2025-01-01", periods=3)
    sa = SmithianAnalyzer(df, "TEST", "/unit")
    sa.calculate_natural_price(window_days=2)
    result = sa.analyze(window_days=2)
    # With prices [10, 11, 12] and window=2, the natural price for the last day is 11.5
    # Market price is 12, so deviation is (12-11.5)/11.5 = 4.35%, which is NEAR_NATURAL (<5%)
    assert result["condition"] == "NEAR_NATURAL"
    assert result["deviation_percent"] > 0
    assert abs(result["deviation_percent"] - 4.35) < 0.1  # approximately 4.35%


def test_above_natural():
    """Test case where market price is significantly above natural price."""
    df = pd.DataFrame({"price": [10, 10, 15]})  # Big jump on last day
    df.index = pd.date_range("2025-01-01", periods=3)
    sa = SmithianAnalyzer(df, "TEST", "/unit")
    sa.calculate_natural_price(window_days=2)
    result = sa.analyze(window_days=2)
    # Natural price should be 12.5, market price is 15, deviation = (15-12.5)/12.5 = 20%
    assert result["condition"] == "ABOVE_NATURAL"
    assert result["deviation_percent"] > 5


def test_below_natural():
    """Test case where market price is significantly below natural price."""
    df = pd.DataFrame({"price": [15, 15, 10]})  # Big drop on last day
    df.index = pd.date_range("2025-01-01", periods=3)
    sa = SmithianAnalyzer(df, "TEST", "/unit")
    sa.calculate_natural_price(window_days=2)
    result = sa.analyze(window_days=2)
    # Natural price should be 12.5, market price is 10, deviation = (10-12.5)/12.5 = -20%
    assert result["condition"] == "BELOW_NATURAL"
    assert result["deviation_percent"] < -5
